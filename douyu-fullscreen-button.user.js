// ==UserScript==
// @name         斗鱼全屏按钮
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  在斗鱼直播间添加倒计时全屏按钮
// <AUTHOR>
// @match        https://www.douyu.com/*
// @match        https://douyu.com/*
// @match        https://m.douyu.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 检测是否在直播间页面
    const isInLiveRoom = () => {
        const url = window.location.href;
        // 匹配斗鱼直播间URL格式：www.douyu.com/数字 或 douyu.com/数字
        const liveRoomPattern = /douyu\.com\/(\d+)(?:\?|$|\/)/;
        return liveRoomPattern.test(url);
    };

    // 全屏功能
    const switchFullMode = () => {
        if (!document.fullscreenElement) {
            const videoContainer = document.querySelector("#js-player-video-case");
            if (videoContainer) {
                videoContainer.requestFullscreen().catch(err => {
                    console.error('全屏请求失败:', err);
                });
            }
        } else {
            document.exitFullscreen();
        }
    };

    // 创建全屏按钮
    const createFullscreenButton = () => {
        // 创建按钮容器
        const button = document.createElement('div');
        button.id = 'douyu-fullscreen-btn';
        button.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            cursor: pointer;
            z-index: 999999;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            font-family: Arial, sans-serif;
            user-select: none;
        `;

        // 创建全屏图标
        const icon = document.createElement('div');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            position: relative;
            margin-bottom: 4px;
        `;
        
        // 创建四个角的图标
        const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        corners.forEach(corner => {
            const cornerElement = document.createElement('div');
            cornerElement.style.cssText = `
                position: absolute;
                width: 8px;
                height: 8px;
                border: 2px solid white;
            `;
            
            switch(corner) {
                case 'top-left':
                    cornerElement.style.cssText += `
                        top: 0;
                        left: 0;
                        border-right: none;
                        border-bottom: none;
                    `;
                    break;
                case 'top-right':
                    cornerElement.style.cssText += `
                        top: 0;
                        right: 0;
                        border-left: none;
                        border-bottom: none;
                    `;
                    break;
                case 'bottom-left':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        left: 0;
                        border-right: none;
                        border-top: none;
                    `;
                    break;
                case 'bottom-right':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        right: 0;
                        border-left: none;
                        border-top: none;
                    `;
                    break;
            }
            icon.appendChild(cornerElement);
        });

        // 创建倒计时文字
        const countdown = document.createElement('div');
        countdown.style.cssText = `
            color: white;
            font-size: 14px;
            font-weight: bold;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        `;

        button.appendChild(icon);
        button.appendChild(countdown);
        document.body.appendChild(button);

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }
            
            @keyframes fadeOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
            
            @keyframes countdownPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);

        // 淡入动画
        setTimeout(() => {
            button.style.animation = 'fadeInScale 0.3s ease-out forwards';
        }, 100);

        // 鼠标悬停效果
        button.addEventListener('mouseenter', () => {
            if (!button.classList.contains('removing')) {
                button.style.background = 'rgba(0, 0, 0, 0.9)';
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
            }
        });

        button.addEventListener('mouseleave', () => {
            if (!button.classList.contains('removing')) {
                button.style.background = 'rgba(0, 0, 0, 0.6)';
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
            }
        });

        // 点击事件
        button.addEventListener('click', () => {
            switchFullMode();
            removeButton();
            console.debug("来自**斗鱼全屏按钮**: 用户点击按钮进入全屏");
        });

        // 倒计时功能
        let timeLeft = 5;
        countdown.textContent = timeLeft;
        
        const countdownInterval = setInterval(() => {
            timeLeft--;
            if (timeLeft > 0) {
                countdown.textContent = timeLeft;
                countdown.style.animation = 'countdownPulse 0.3s ease-out';
                setTimeout(() => {
                    countdown.style.animation = '';
                }, 300);
            } else {
                clearInterval(countdownInterval);
                removeButton();
                console.debug("来自**斗鱼全屏按钮**: 倒计时结束，按钮自动消失");
            }
        }, 1000);

        // 移除按钮函数
        const removeButton = () => {
            if (button.classList.contains('removing')) return;
            
            button.classList.add('removing');
            clearInterval(countdownInterval);
            button.style.animation = 'fadeOut 0.3s ease-out forwards';
            
            setTimeout(() => {
                if (document.body.contains(button)) {
                    document.body.removeChild(button);
                }
            }, 300);
        };

        return { button, removeButton };
    };

    // 主函数
    const init = () => {
        // 延迟执行，确保页面加载完成，且仅在直播间页面显示
        setTimeout(() => {
            if (isInLiveRoom()) {
                createFullscreenButton();
                console.debug("来自**斗鱼全屏按钮**: 检测到直播间页面，全屏按钮已显示");
            } else {
                console.debug("来自**斗鱼全屏按钮**: 非直播间页面，跳过按钮显示");
            }
        }, 2000); // 延迟2秒显示按钮
    };

    // 启动
    init();
})();
