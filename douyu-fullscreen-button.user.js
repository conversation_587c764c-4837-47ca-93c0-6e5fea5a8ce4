// ==UserScript==
// @name         斗鱼全屏按钮 - Douyu Fullscreen Button
// @name:en      Douyu Fullscreen Button
// @namespace    https://github.com/Li-Jinxuan/douyuScript
// @version      1.0.0
// @description  为斗鱼直播间添加智能全屏按钮，支持透明度倒计时效果，提供优雅的全屏体验
// @description:en Add smart fullscreen button to Douyu live rooms with opacity countdown effect
// <AUTHOR>
// @match        https://www.douyu.com/*
// @match        https://douyu.com/*
// @match        https://m.douyu.com/*
// @icon         data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMzMzMiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im04IDNhMSAxIDAgMCAwLTEgMXY0YTEgMSAwIDAgMCAxIDFoNGExIDEgMCAwIDAgMS0xVjRhMSAxIDAgMCAwLTEtMXoiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im0xNSAzaDRhMSAxIDAgMCAxIDEgMXY0Ii8+CjxwYXRoIGQ9Im0yMSAxNXY0YTEgMSAwIDAgMS0xIDFoLTQiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im05IDIxSDVhMSAxIDAgMCAxLTEtMXYtNCIvPgo8cGF0aCBkPSJtMyA5VjVhMSAxIDAgMCAxIDEtMWg0Ii8+Cjwvc3Ryb2tlPgo8L3N2Zz4KPC9zdmc+
// @license      MIT
// @supportURL   https://github.com/Li-Jinxuan/douyuScript/issues
// @updateURL    https://github.com/Li-Jinxuan/douyuScript/raw/main/douyu-fullscreen-button.user.js
// @downloadURL  https://github.com/Li-Jinxuan/douyuScript/raw/main/douyu-fullscreen-button.user.js
// @grant        none
// @run-at       document-end
// @noframes
// ==/UserScript==

/**
 * 斗鱼全屏按钮脚本
 *
 * 功能说明：
 * - 智能检测斗鱼直播间页面，仅在直播间显示全屏按钮
 * - 按钮定位在播放器右下角内部，不遮挡重要内容
 * - 5秒透明度倒计时，提供优雅的视觉反馈
 * - 支持点击立即全屏，或自动消失
 * - 现代化UI设计，包含悬停效果和流畅动画
 *
 * 技术特点：
 * - 使用 #js-player-video 容器进行精确定位
 * - 透明度渐变倒计时（1.0 → 0.8 → 0.6 → 0.4 → 0.2 → 0）
 * - 高性能动画和事件处理
 * - 完整的错误处理和调试日志
 *
 * 兼容性：支持所有现代浏览器和斗鱼网页版
 *
 * @version 1.0.0
 * <AUTHOR>
 * @license MIT
 */

(function() {
    'use strict';

    // 检测是否在直播间页面
    const isInLiveRoom = () => {
        const url = window.location.href;
        // 匹配斗鱼直播间URL格式：www.douyu.com/数字 或 douyu.com/数字
        const liveRoomPattern = /douyu\.com\/(\d+)(?:\?|$|\/)/;
        return liveRoomPattern.test(url);
    };

    // 全屏功能
    const switchFullMode = () => {
        if (!document.fullscreenElement)
        {
            const videoContainer = document.querySelector("#js-player-video-case");
            if (videoContainer)
            {
                videoContainer.requestFullscreen().catch(err => {
                    console.error('全屏请求失败:', err);
                });
            }
        } else
        {
            document.exitFullscreen();
        }
    };

    // 创建全屏按钮
    const createFullscreenButton = () => {
        // 查找播放器容器
        const playerContainer = document.querySelector("#js-player-video");
        if (!playerContainer)
        {
            console.error('来自**斗鱼全屏按钮**: 未找到播放器容器 #js-player-video');
            return null;
        }

        // 设置播放器容器为相对定位
        if (getComputedStyle(playerContainer).position === 'static')
        {
            playerContainer.style.position = 'relative';
        }

        // 创建按钮容器
        const button = document.createElement('div');
        button.id = 'douyu-fullscreen-btn';
        button.style.cssText = `
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            cursor: pointer;
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            font-family: Arial, sans-serif;
            user-select: none;
        `;

        // 创建全屏图标
        const icon = document.createElement('div');
        icon.style.cssText = `
            width: 24px;
            height: 24px;
            position: relative;
            margin-bottom: 4px;
        `;

        // 创建四个角的图标
        const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];
        corners.forEach(corner => {
            const cornerElement = document.createElement('div');
            cornerElement.style.cssText = `
                position: absolute;
                width: 8px;
                height: 8px;
                border: 2px solid white;
            `;

            switch (corner)
            {
                case 'top-left':
                    cornerElement.style.cssText += `
                        top: 0;
                        left: 0;
                        border-right: none;
                        border-bottom: none;
                    `;
                    break;
                case 'top-right':
                    cornerElement.style.cssText += `
                        top: 0;
                        right: 0;
                        border-left: none;
                        border-bottom: none;
                    `;
                    break;
                case 'bottom-left':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        left: 0;
                        border-right: none;
                        border-top: none;
                    `;
                    break;
                case 'bottom-right':
                    cornerElement.style.cssText += `
                        bottom: 0;
                        right: 0;
                        border-left: none;
                        border-top: none;
                    `;
                    break;
            }
            icon.appendChild(cornerElement);
        });

        button.appendChild(icon);
        playerContainer.appendChild(button);

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @keyframes fadeOut {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
        `;
        document.head.appendChild(style);

        // 淡入动画
        setTimeout(() => {
            button.style.animation = 'fadeInScale 0.3s ease-out forwards';
        }, 100);

        // 鼠标悬停效果
        button.addEventListener('mouseenter', () => {
            if (!button.classList.contains('removing'))
            {
                button.style.background = 'rgba(0, 0, 0, 0.9)';
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
            }
        });

        button.addEventListener('mouseleave', () => {
            if (!button.classList.contains('removing'))
            {
                button.style.background = 'rgba(0, 0, 0, 0.6)';
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
            }
        });

        // 点击事件
        button.addEventListener('click', () => {
            switchFullMode();
            removeButton();
            console.debug("来自**斗鱼全屏按钮**: 用户点击按钮进入全屏");
        });

        // 透明度渐变倒计时功能
        let timeLeft = 5;
        let currentOpacity = 1.0;

        const countdownInterval = setInterval(() => {
            timeLeft--;
            currentOpacity = timeLeft * 0.2; // 每秒递减0.2透明度

            if (timeLeft > 0)
            {
                button.style.opacity = currentOpacity;
                console.debug(`来自**斗鱼全屏按钮**: 倒计时 ${timeLeft}秒，透明度: ${currentOpacity}`);
            } else
            {
                clearInterval(countdownInterval);
                removeButton();
                console.debug("来自**斗鱼全屏按钮**: 倒计时结束，按钮自动消失");
            }
        }, 1000);

        // 移除按钮函数
        const removeButton = () => {
            if (button.classList.contains('removing')) return;

            button.classList.add('removing');
            clearInterval(countdownInterval);
            button.style.animation = 'fadeOut 0.3s ease-out forwards';

            setTimeout(() => {
                if (playerContainer.contains(button))
                {
                    playerContainer.removeChild(button);
                }
            }, 300);
        };

        return { button, removeButton };
    };

    // 主函数
    const init = () => {
        // 延迟执行，确保页面加载完成，且仅在直播间页面显示
        setTimeout(() => {
            if (isInLiveRoom())
            {
                createFullscreenButton();
                console.debug("来自**斗鱼全屏按钮**: 检测到直播间页面，全屏按钮已显示");
            } else
            {
                console.debug("来自**斗鱼全屏按钮**: 非直播间页面，跳过按钮显示");
            }
        }, 2000); // 延迟2秒显示按钮
    };

    // 启动
    init();
})();
